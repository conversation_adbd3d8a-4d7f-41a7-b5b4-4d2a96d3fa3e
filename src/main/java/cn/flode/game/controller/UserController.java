package cn.flode.game.controller;

import cn.flode.game.controller.dto.WeChatLoginDTO;
import cn.flode.game.controller.vo.LoginResult;
import cn.flode.game.controller.vo.UserInfo;
import cn.flode.game.service.UserService;
import cn.flode.game.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user")
public class UserController {

  private final UserService userService;

  /**
   * 微信小程序登录
   *
   * @param loginDTO 微信登录信息
   * @return 登录结果
   */
  @Operation(summary = "微信小程序登录", description = "通过微信小程序code进行登录")
  @PostMapping("/wechat-login")
  public LoginResult weChatLogin(
      @Parameter(description = "微信小程序登录信息", required = true) @Validated @RequestBody
          WeChatLoginDTO loginDTO) {
    return userService.weChatLogin(loginDTO);
  }

  /**
   * 获取当前用户信息
   *
   * @return 用户信息
   */
  @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的基本信息")
  @SecurityRequirement(name = "sessionAuth")
  @GetMapping("/info")
  public UserInfo getUserInfo() {
    Long userId = SecurityUtils.currentUserId();
    String nickName = SecurityUtils.currentNickName();
    return UserInfo.of(userId, nickName);
  }

  /**
   * 用户登出
   *
   * @return 登出结果
   */
  @Operation(summary = "用户登出", description = "清除用户登录状态")
  @PostMapping("/logout")
  public String logout() {
    return userService.logout();
  }

  /**
   * 获取用户详细信息（别名接口）
   *
   * @return 用户信息
   */
  @Operation(summary = "获取用户详细信息", description = "获取当前登录用户的详细信息（与/info接口相同）")
  @SecurityRequirement(name = "sessionAuth")
  @GetMapping("/profile")
  public UserInfo getUserProfile() {
    return getUserInfo();
  }
}
